package com.ruoyi.web.controller.common;

import cn.afterturn.easypoi.word.entity.MyXWPFDocument;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.app.controller.support.ftp.FtpTemplate;
import com.ruoyi.app.controller.support.print.PrintUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.UploadAddress;
import com.ruoyi.common.domain.bo.SendEmailBO;
import com.ruoyi.common.domain.bo.UploadBO;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.DataType;
import com.ruoyi.common.enums.FlagBargeState;
import com.ruoyi.common.enums.WxOperateStatus;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.CargocmentdetailService;
import com.ruoyi.common.service.FtpService;
import com.ruoyi.common.service.UploadAddressService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.email.SendEmailUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.databarge.SendCollectionLetterDto;
import com.ruoyi.databarge.domain.Pb6Bargework;
import com.ruoyi.databarge.service.Pb6BargeworkService;
import com.ruoyi.framework.config.ServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private FtpService ftpService;
    @Autowired
    private SendEmailUtils sendEmailUtils;
    @Autowired
    private FtpTemplate ftpTemplate;
    @Autowired
    private Pb6BargeworkService pb6BargeworkService;
    @Autowired
    private PrintUtil printUtil;
    @Autowired
    private CargocmentdetailService cargocmentdetailService;
    @Autowired
    private UploadAddressService uploadAddressService;
    @Autowired
    private FddCommonService fddCommonService;

    @Autowired
    private JavaMailSender mailSender;
    @Value("${spring.mail.username}")
    private String username;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("common/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.isValidFilename(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, realFileName));
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求
     */
    @PostMapping("/common/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("url", url);
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/common/download/resource")
    public void resourceDownload(String name, HttpServletRequest request, HttpServletResponse response) throws Exception
    {
        // 本地资源路径
        String localPath = RuoYiConfig.getProfile();
        // 数据库资源地址
        String downloadPath = localPath + StringUtils.substringAfter(name, Constants.RESOURCE_PREFIX);
        // 下载名称
        String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data");
        response.setHeader("Content-Disposition",
                "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, downloadName));
        FileUtils.writeBytes(downloadPath, response.getOutputStream());
    }

    /**
     * 小程序端通用上传请求
     */
    @PostMapping("/common/weChatUploadFile")
    public AjaxResult weChatUploadFile(@RequestParam("fileName") MultipartFile file,
                                       @RequestParam("linkId") Long linkId,
                                       @RequestParam("linkType") String linkType,
                                       @RequestParam("dataType") Integer dataType,
                                       @RequestParam("linkCompanyId") Long linkCompanyId)
    {
        if (file == null) {
            log.error("文件不能为空");
            return AjaxResult.error("文件不能为空");
        }

        if (StringUtils.isBlank(linkType)) {
            log.error("资料类型不能为空");
            return AjaxResult.error("资料类型不能为空");
        }

        UploadBO uploadBO = new UploadBO(linkId, linkType, linkCompanyId, dataType);

        //计时器
        log.info("开始上传");
        TimeInterval timer = DateUtil.timer();
        AjaxResult result = ftpService.ftpUpload(file, uploadBO);
        //返回花费时间，并重置开始时间
        log.info("上传结束，花费时间：{}ms",timer.intervalRestart());
        return result;
    }

    /**
     * 小程序端通用下载
     */
    @PostMapping("/common/weChatDownload")
    public void weChatDownload(String url, HttpServletResponse response, HttpServletRequest request) {
        ftpService.download(url, response, request);
    }

    /**
     * web端上传
     * @param file 文件
     * @param userName 用户名
     * @return
     */
    @PostMapping("/common/webUpload")
    public AjaxResult webUpload(@RequestParam("fileName") MultipartFile file,@RequestParam("userName") String userName) {
        if (file == null) {
            log.error("文件不能为空");
            return AjaxResult.error("文件不能为空");
        }
        if (StringUtils.isNotEmpty(userName)) {
            log.error("用户名不能为空");
            return AjaxResult.error("用户名不能为空");
        }
        return null;
    }

    @PostMapping("/common/selaUpload")
    public AjaxResult selaUpload(@RequestBody String sealImg) {
        if (StringUtils.isBlank(sealImg)) {
            log.info("印章为空");
            return AjaxResult.error("印章为空");
        }
        return AjaxResult.success(ftpService.selaUpload(sealImg));
    }

    /**
     * 文件以邮件附件的形式发送
     * @param sendEmailBO
     * @return
     */
    @PostMapping("/common/sendEmail")
    public AjaxResult sendEmail(@RequestBody SendEmailBO sendEmailBO) throws Exception {

        if (StringUtils.isBlank(sendEmailBO.getToEmail())) {
            log.error("邮箱名称不能为空");
            return AjaxResult.error("邮箱名称不能为空");
        }

        String waterwayCargoId = sendEmailBO.getWaterwayCargoId();
        if (StringUtils.isBlank(waterwayCargoId)) {
            log.error("水路运单编号不能为空");
            return AjaxResult.error("目前暂时没有水路运单");
        }

        QueryWrapper<Cargoconsignmentdetail> detailWrapper = new QueryWrapper<>();
        detailWrapper.eq("waterwaycargoid", sendEmailBO.getWaterwayCargoId());
        Cargoconsignmentdetail detail = cargocmentdetailService.getBaseMapper().selectOne(detailWrapper);

        if (StringUtils.isNull(detail)) {
            return AjaxResult.error("未找到对应托运单明细数据");
        }

        // 小程序操作状态
        Integer wxOperateState = detail.getWxOperateState();
        // 驳船状态
        String flagBargeState = detail.getFlagBargeState();

        HashMap<String, byte[]> map = new HashMap<>();
        Pb6Bargework pb6Bargework = pb6BargeworkService.getBaseMapper().selectOne(new QueryWrapper<Pb6Bargework>().eq("WATERWAYCARGOID", sendEmailBO.getWaterwayCargoId()));

        // 获取文件
        List<UploadAddress> list = uploadAddressService.getUploadAddressList(null, waterwayCargoId);
        if (list == null || list.size() <= 0) {
            return AjaxResult.error("暂时没有可下载文件");
        }
        String emailTitle="水路运单"+waterwayCargoId+"资料";
        if (wxOperateState >= WxOperateStatus.WAIT_BARGE_RESERVE.getCode()
                && Integer.parseInt(flagBargeState) >= Integer.parseInt(FlagBargeState.STOWAGE.getCode())) {
            // 待支付状态之后
            list.forEach(item -> {
                if (DataType.CONSIGN_WATER_ORDER.getCode().equals(item.getDataType())) {
                    ByteArrayOutputStream outputStream = fddCommonService.downloadContract(item.getContractNo());
                    map.put("水路货物运单"+waterwayCargoId+".pdf", outputStream.toByteArray());
                    IoUtil.close(outputStream);
                }
            });

            if (WxOperateStatus.PASS_BARGE_RESERVE.getCode().equals(wxOperateState)
                    && FlagBargeState.LEAVING.getCode().equals(flagBargeState))  {
                // 已完成
                if (pb6Bargework!=null) {
                    list.forEach(item -> {
                        if (DataType.CONSIGN_CERTIFICATE.getCode().equals(item.getDataType())) {
                            ByteArrayOutputStream outputStream = fddCommonService.downloadContract(item.getContractNo());
                            map.put("广州港新沙港务有限公司驳船装货交接凭证" + waterwayCargoId + ".pdf", outputStream.toByteArray());
                            IoUtil.close(outputStream);
                        }
                    });
                }

                list.forEach(item -> {
                    if (DataType.CONSIGN_HANDOVER.getCode().equals(item.getDataType())) {
                        ByteArrayOutputStream outputStream = fddCommonService.downloadContract(item.getContractNo());
                        map.put("货物交接清单" + waterwayCargoId + ".pdf", outputStream.toByteArray());
                        IoUtil.close(outputStream);
                    }
                });
            }
        }

        if (map.size() <= 0) {
            return AjaxResult.error("暂时没有可下载文件");
        }

        String content = StringUtils.format("文件已发送到邮箱，请查看附件，请勿回复。请使用电脑网页查看邮箱，手机QQ邮箱可能显示附件异常！");

        return sendEmailUtils.sendSimpleMail(sendEmailBO.getToEmail(), emailTitle, content, map);
    }

    @PostMapping("/common/sendCollectionLetter")
    @Log(title = "对外邮件发送接口",businessType = BusinessType.IMPORT)
    public AjaxResult sendCollectionLetter(SendCollectionLetterDto sendCollectionLetterDto){
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        System.out.println(sendCollectionLetterDto);
        MultipartFile file = sendCollectionLetterDto.getFile();
        MimeMessageHelper helper;
        try {
            // 设置utf-8或GBK编码，否则邮件会有乱码，true表示为multipart邮件
            helper = new MimeMessageHelper(mimeMessage,true, "UTF-8");

            // 设置发送人邮件地址
            helper.setFrom(username);

            // 邮件接收地址
            helper.setTo(sendCollectionLetterDto.getEmail());

            // 设置抄送 邮箱地址
            // helper.setBcc(new InternetAddress());
            // 设置发送邮件的标题
            helper.setSubject(sendCollectionLetterDto.getTitle());

            // 设置邮件内容，参数true表示启用html格式
            helper.setText(sendCollectionLetterDto.getContent(), true);

            // 获取文件名
            String fileName = file.getOriginalFilename();
            // 获取文件后缀
            String prefix = fileName.substring(fileName.lastIndexOf("."));

            try {
                File file1 = File.createTempFile(fileName, prefix);
                file.transferTo(file1);
                // 第一个参数附件名，第二个参数附件
                helper.addAttachment(MimeUtility.encodeWord(fileName, "UTF-8", "B"), new FileSystemResource(file1));
            } catch (Exception e) {
                e.printStackTrace();
            }
            //log.warn("发送邮件：" + subject + "至" + toEmail);

            mailSender.send(mimeMessage);
        } catch (Exception e) {
            log.warn("邮件发送失败");
            e.printStackTrace();
            return AjaxResult.error("邮件发送失败");
        }
        return AjaxResult.success("邮件发送成功");
    }

    @GetMapping("/common/downloadFtpFile")
    public void downloadFtpFile(@RequestParam("fileName") String fileName, HttpServletResponse response, HttpServletRequest request) throws Exception {
        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
        InputStream in=new ClassPathResource(fileName).getInputStream();// 类路径下的输入流
        MyXWPFDocument docx=new MyXWPFDocument(in);
        docx.write(byteArrayOutputStream);
        response.setContentLength(byteArrayOutputStream.size());
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data");
        response.setHeader("Content-Disposition",
                "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, fileName));
        response.resetBuffer();
        response.getOutputStream().write(byteArrayOutputStream.toByteArray());
    }

    public String saveSeal(String base64, Long userId) {
        String path = null;
        if (StringUtils.isNotBlank(base64)) {

            // 测试，直接存在本地 d盘 uploadTest\barge目录下
//             path = "D:/uploadTest/seal/" + IdUtil.simpleUUID() + ".png";

            path = "/upload/seal/" + IdUtil.simpleUUID() + ".png";
            // path = "/gzcw/uploadFiles/seal/" + IdUtil.simpleUUID() + ".png";
            String base64Data = base64.substring(base64.indexOf(","));
            byte[] bytes = Base64.decode(base64Data);

            try (InputStream in = new ByteArrayInputStream(bytes)) {
                ftpTemplate.upload(path, in);
            } catch (IOException e) {
                e.printStackTrace();
                throw new CustomException("上传电子印章失败",e);
            }
        }
        return path;
    }
}
