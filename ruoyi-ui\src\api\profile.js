import request from '@/utils/request'

// 获取个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

// 修改个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}

// 修改密码
export function updateUserPwd(oldPassword, newPassword) {
  const data = new URLSearchParams()
  data.append('oldPassword', oldPassword)
  data.append('newPassword', newPassword)
  
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 上传头像
export function uploadAvatar(data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: data
  })
}
