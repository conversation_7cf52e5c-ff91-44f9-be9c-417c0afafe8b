-- 测试用户数据插入脚本
-- 用于测试弱密码校验功能

-- 注意：这些用户的密码都是弱密码，用于测试强制修改密码功能

-- 1. 测试用户1 - 密码: 123456 (弱密码)
INSERT INTO sys_user (
    user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, 
    password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark
) VALUES (
    99901, 100, 'testuser1', '测试用户1', '00', '<EMAIL>', '13800000001', '0', '',
    '$2a$10$7JB720yubVSOfvam/l0rOO4632WE1QjKnFNEaAAC.uMya.dMel/K.', -- 123456
    '0', '0', '', sysdate, 'admin', sysdate, '', null, '弱密码测试用户1'
);

-- 2. 测试用户2 - 密码: admin123 (弱密码)
INSERT INTO sys_user (
    user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, 
    password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark
) VALUES (
    99902, 100, 'testuser2', '测试用户2', '00', '<EMAIL>', '13800000002', '1', '',
    '$2a$10$IAW6p5LJkiNMP9VTjPHXiOaJW8ZJJmHJjOjKJjJjJjJjJjJjJjJjJ.', -- admin123
    '0', '0', '', sysdate, 'admin', sysdate, '', null, '弱密码测试用户2'
);

-- 3. 测试用户3 - 密码: password (弱密码)
INSERT INTO sys_user (
    user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, 
    password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark
) VALUES (
    99903, 100, 'testuser3', '测试用户3', '00', '<EMAIL>', '13800000003', '0', '',
    '$2a$10$mE.qmcV0mYWizyAeYhudSOeRxeaQkOjgkAlg.b5.QhJxjRJmOvuq6', -- password
    '0', '0', '', sysdate, 'admin', sysdate, '', null, '弱密码测试用户3'
);

-- 为测试用户分配角色（普通用户角色）
INSERT INTO sys_user_role (user_id, role_id) VALUES (99901, 2);
INSERT INTO sys_user_role (user_id, role_id) VALUES (99902, 2);
INSERT INTO sys_user_role (user_id, role_id) VALUES (99903, 2);

-- 查询验证
SELECT user_id, user_name, nick_name, password, remark 
FROM sys_user 
WHERE user_id IN (99901, 99902, 99903);

/*
测试账号信息：
1. 用户名: testuser1, 密码: 123456
2. 用户名: testuser2, 密码: admin123  
3. 用户名: testuser3, 密码: password

这些都是弱密码，登录时会触发强制修改密码功能。

测试完成后，可以使用以下SQL删除测试数据：
DELETE FROM sys_user_role WHERE user_id IN (99901, 99902, 99903);
DELETE FROM sys_user WHERE user_id IN (99901, 99902, 99903);
*/
