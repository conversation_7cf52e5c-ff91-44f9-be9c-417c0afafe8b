# 弱密码校验功能测试指南

## 测试准备

### 方案1：使用临时测试开关（推荐）

1. **启用测试模式**：
   - 文件位置：`ruoyi/ruoyi-common/src/main/java/com/ruoyi/common/utils/PasswordValidationUtils.java`
   - 将 `SKIP_VALIDATION_FOR_TEST` 设置为 `true`（已设置）
   - 重新编译项目

2. **创建测试用户**：
   - 在测试模式下，可以创建任意密码的用户
   - 建议创建密码为 `123456`、`admin123`、`password` 等弱密码的用户

3. **切换到正式模式**：
   - 将 `SKIP_VALIDATION_FOR_TEST` 设置为 `false`
   - 重新编译项目
   - 现在弱密码用户登录时会触发强制修改密码

### 方案2：使用SQL脚本

1. **执行测试用户创建脚本**：
   ```sql
   -- 执行 测试用户数据.sql
   ```

2. **或执行密码重置脚本**：
   ```sql
   -- 执行 重置用户密码为弱密码.sql
   ```

## 测试场景

### 1. 登录时密码强度校验

**测试步骤**：
1. 确保 `SKIP_VALIDATION_FOR_TEST = false`
2. 使用弱密码账号登录（如：testuser1/123456）
3. 验证是否弹出强制修改密码弹窗

**预期结果**：
- 登录成功但不跳转到首页
- 显示强制修改密码弹窗
- 弹窗显示具体的密码强度不足原因
- 弹窗无法关闭

### 2. 强制修改密码功能

**测试步骤**：
1. 在强制修改密码弹窗中输入：
   - 当前密码：123456
   - 新密码：123456（相同弱密码）
2. 点击"立即修改"

**预期结果**：
- 提示密码强度不足，无法修改

**测试步骤**：
1. 输入强密码（如：Test123456!）
2. 确认密码：Test123456!
3. 点击"立即修改"

**预期结果**：
- 修改成功
- 自动退出登录
- 要求重新登录

### 3. 其他密码校验场景

**新增用户**：
1. 后台管理 → 用户管理 → 新增用户
2. 输入弱密码
3. 验证是否提示密码强度不足

**重置密码**：
1. 后台管理 → 用户管理 → 重置密码
2. 输入弱密码
3. 验证是否提示密码强度不足

**个人修改密码**：
1. 个人中心 → 修改密码
2. 输入弱密码
3. 验证是否提示密码强度不足

**用户导入**：
1. 确保系统初始密码为弱密码
2. 尝试导入用户
3. 验证是否阻止导入

## 测试用例

### 弱密码示例
- `123456` - 纯数字
- `password` - 纯字母
- `admin123` - 包含用户名
- `12345678` - 长度符合但复杂度不够
- `ABCDEFGH` - 纯大写字母

### 强密码示例
- `Test123456!` - 包含大小写字母、数字、特殊字符
- `MyPassword@2024` - 复杂度足够
- `Secure#Pass123` - 符合所有要求

## 测试数据清理

### 删除测试用户
```sql
-- 删除测试用户
DELETE FROM sys_user_role WHERE user_id IN (99901, 99902, 99903, 99999);
DELETE FROM sys_user WHERE user_id IN (99901, 99902, 99903, 99999);
```

### 恢复生产配置
1. 将 `SKIP_VALIDATION_FOR_TEST` 设置为 `false`
2. 重新编译部署

## 注意事项

1. **测试环境专用**：测试开关和弱密码账号仅用于测试环境
2. **生产环境安全**：生产环境必须关闭测试开关
3. **密码加密**：SQL脚本中的密码已经是BCrypt加密后的值
4. **权限配置**：测试用户已分配适当的角色权限

## 故障排除

### 问题1：弹窗不显示
- 检查浏览器控制台是否有JavaScript错误
- 确认后端返回了 `forceChangePassword` 字段

### 问题2：密码修改失败
- 检查新密码是否符合强度要求
- 确认当前密码输入正确

### 问题3：测试开关不生效
- 确认代码修改后重新编译
- 检查控制台是否输出"密码强度校验已被临时禁用"

## 测试报告模板

| 测试场景 | 测试结果 | 备注 |
|---------|---------|------|
| 弱密码登录触发强制修改 | ✅/❌ | |
| 强制修改密码弹窗显示 | ✅/❌ | |
| 弱密码修改被拒绝 | ✅/❌ | |
| 强密码修改成功 | ✅/❌ | |
| 修改后重新登录 | ✅/❌ | |
| 新增用户密码校验 | ✅/❌ | |
| 重置密码校验 | ✅/❌ | |
| 个人修改密码校验 | ✅/❌ | |
