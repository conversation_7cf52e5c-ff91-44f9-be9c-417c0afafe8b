<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">广州港船务水上过驳服务平台</h3>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <!-- <el-form-item prop="code">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" />
        </div>
      </el-form-item> -->
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright &copy; {{getYear}} 广州港数据科技有限公司 All Rights Reserved</span>
    </div>

    <!-- {{ AURA-X: Add - 强制修改密码弹窗. Approval: 寸止(ID:1722330005). }} -->
    <!-- 强制修改密码弹窗 -->
    <el-dialog
      title="密码强度不足，请立即修改密码"
      :visible.sync="forceChangePasswordVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div style="margin-bottom: 20px; color: #E6A23C;">
        <i class="el-icon-warning"></i>
        <span>您的密码强度不符合安全要求：{{ passwordWeakReason }}</span>
      </div>

      <el-form ref="changePasswordForm" :model="changePasswordForm" :rules="changePasswordRules" label-width="120px">
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input
            v-model="changePasswordForm.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="changePasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model="changePasswordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleForceChangePassword" :loading="changePasswordLoading">
          立即修改
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import { updateUserPwd } from "@/api/profile";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      cookiePassword: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        // code: "",
        uuid: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "用户名不能为空" }
        ],
        password: [
          { required: true, trigger: "blur", message: "密码不能为空" }
        ],
        // code: [{ required: true, trigger: "change", message: "验证码不能为空" }]
      },
      loading: false,
      redirect: undefined,
      // {{ AURA-X: Add - 强制修改密码相关数据. Approval: 寸止(ID:1722330005). }}
      forceChangePasswordVisible: false,
      passwordWeakReason: '',
      changePasswordLoading: false,
      changePasswordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      changePasswordRules: {
        oldPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 8, max: 32, message: '密码长度必须在8到32个字符之间', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: (rule, value, callback) => this.validateConfirmPassword(rule, value, callback), trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  computed: {
    getYear(){
      return new Date().getFullYear()
    }
  },
  created() {
    // this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.loginForm.uuid = res.uuid;
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store
            .dispatch("Login", this.loginForm)
            .then((res) => {
              // {{ AURA-X: Add - 处理登录时的密码强度检查，如不符合要求则强制修改密码. Approval: 寸止(ID:1722330005). }}
              if (res && res.forceChangePassword) {
                // 密码强度不足，显示强制修改密码弹窗
                this.showForceChangePasswordDialog(res.passwordWeakReason);
                return;
              }

              const userName = localStorage.getItem('userName')
              if(userName === 'xsadmin') {
                this.$router.push({
                  name: 'Port'
                })
              } else {
                this.$router.push({ path: this.redirect || "/" });
              }
            })
            .catch(() => {
              this.loading = false;
              this.getCode();
            });
        }
      });
    },
    // {{ AURA-X: Add - 强制修改密码相关方法. Approval: 寸止(ID:1722330005). }}
    // 显示强制修改密码弹窗
    showForceChangePasswordDialog(reason) {
      this.passwordWeakReason = reason;
      this.forceChangePasswordVisible = true;
      this.loading = false;
      // 将当前密码设置为旧密码
      this.changePasswordForm.oldPassword = this.loginForm.password;
    },

    // 确认密码验证
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.changePasswordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    },

    // 处理强制修改密码
    handleForceChangePassword() {
      this.$refs.changePasswordForm.validate(valid => {
        if (valid) {
          this.changePasswordLoading = true;

          // {{ AURA-X: Fix - 修复API调用错误，使用正确的修改密码接口. Approval: 寸止(ID:1722330007). }}
          // 使用专门的修改密码API
          updateUserPwd(this.changePasswordForm.oldPassword, this.changePasswordForm.newPassword)
            .then(response => {
              if (response && response.code === 200) {
                this.$message.success('密码修改成功，请重新登录');
                // 关闭弹窗，清空表单
                this.forceChangePasswordVisible = false;
                this.resetChangePasswordForm();
                // 清除登录状态，要求重新登录
                this.$store.dispatch('LogOut').then(() => {
                  location.reload();
                });
              } else {
                this.$message.error((response && response.msg) || '密码修改失败');
              }
            })
            .catch(error => {
              console.error('修改密码失败:', error);
              if (error.response && error.response.data) {
                this.$message.error(error.response.data.msg || '密码修改失败');
              } else {
                this.$message.error('密码修改失败，请重试');
              }
            })
            .finally(() => {
              this.changePasswordLoading = false;
            });
        }
      });
    },

    // 重置修改密码表单
    resetChangePasswordForm() {
      this.changePasswordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
      if (this.$refs.changePasswordForm) {
        this.$refs.changePasswordForm.resetFields();
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/image/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

/* {{ AURA-X: Add - 强制修改密码弹窗样式. Approval: 寸止(ID:1722330005). }} */
.el-dialog__header {
  background-color: #f56c6c;
  color: white;
  padding: 15px 20px;
}

.el-dialog__title {
  color: white;
  font-weight: bold;
}

.dialog-footer {
  text-align: center;
}

.el-dialog__body {
  padding: 30px 20px;
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
</style>
