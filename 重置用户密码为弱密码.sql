-- 重置现有用户密码为弱密码（用于测试）
-- 注意：仅用于测试环境，生产环境请勿使用

-- 查看当前所有用户
SELECT user_id, user_name, nick_name, status FROM sys_user WHERE del_flag = '0' AND status = '0';

-- 备份当前密码（可选）
-- CREATE TABLE sys_user_password_backup AS 
-- SELECT user_id, user_name, password, update_time FROM sys_user WHERE del_flag = '0';

-- 方案1：将admin用户密码重置为 123456 (弱密码)
UPDATE sys_user 
SET password = '$2a$10$7JB720yubVSOfvam/l0rOO4632WE1QjKnFNEaAAC.uMya.dMel/K.', -- 123456
    update_time = sysdate,
    update_by = 'system'
WHERE user_name = 'admin' AND del_flag = '0';

-- 方案2：创建一个新的测试管理员账号，密码为弱密码
INSERT INTO sys_user (
    user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, 
    password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark
) VALUES (
    99999, 100, 'testadmin', '测试管理员', '00', '<EMAIL>', '13900000000', '0', '',
    '$2a$10$7JB720yubVSOfvam/l0rOO4632WE1QjKnFNEaAAC.uMya.dMel/K.', -- 123456
    '0', '0', '', sysdate, 'admin', sysdate, '', null, '弱密码测试管理员'
);

-- 为测试管理员分配管理员角色
INSERT INTO sys_user_role (user_id, role_id) VALUES (99999, 1);

-- 验证更新结果
SELECT user_id, user_name, nick_name, 
       CASE WHEN password = '$2a$10$7JB720yubVSOfvam/l0rOO4632WE1QjKnFNEaAAC.uMya.dMel/K.' 
            THEN '密码已设置为123456' 
            ELSE '密码未更改' 
       END as password_status
FROM sys_user 
WHERE user_name IN ('admin', 'testadmin') AND del_flag = '0';

/*
使用说明：
1. 执行上述SQL后，可以使用以下账号测试：
   - 用户名: testadmin, 密码: 123456 (新创建的测试管理员)
   - 用户名: admin, 密码: 123456 (如果选择重置admin密码)

2. 这些密码都是弱密码，登录时会触发强制修改密码功能

3. 测试完成后恢复：
   -- 删除测试管理员
   DELETE FROM sys_user_role WHERE user_id = 99999;
   DELETE FROM sys_user WHERE user_id = 99999;
   
   -- 如果修改了admin密码，需要重新设置强密码
   UPDATE sys_user 
   SET password = '$2a$10$强密码的加密值'
   WHERE user_name = 'admin';
*/
